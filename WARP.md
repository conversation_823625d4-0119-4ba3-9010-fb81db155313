# WARP.md

This file provides guidance to <PERSON><PERSON> (warp.dev) when working with code in this repository.

## Project Overview

**@yummacss/nitro** is the engine behind Yumma CSS - a utility-first CSS framework with abbreviated styles. This package provides the core functionality for extracting CSS classes from source files and generating corresponding CSS output.

## Architecture

### Core Components

1. **Extractor** (`src/extractor.ts`): Scans source files using glob patterns and extracts Yumma CSS class names using regex patterns
2. **Generator** (`src/generator.ts`): Takes extracted class names and generates corresponding CSS rules using utilities from `@yummacss/api`
3. **Configuration** (`src/config/schema.ts`): Zod schema for project configuration including source patterns, output paths, and build options
4. **CSS Reset** (`src/reset/base.ts`): Modern CSS reset with sensible defaults for typography, forms, and layout

### Key Patterns

- **Token Extraction**: Uses multiple regex patterns to find CSS classes in various contexts (HTML attributes, template literals, etc.)
- **CSS Rule Generation**: Matches class names against utility definitions from `@yummacss/api` and generates CSS rules with proper escaping
- **Media Queries & Variants**: Supports responsive prefixes (e.g., `sm:`, `md:`) and pseudo-class variants (e.g., `hover:`, `focus:`)
- **Sorted Output**: CSS rules are generated in alphabetical order for consistency

### Dependencies

- **@yummacss/api**: Provides utility definitions and type interfaces for CSS generation
- **globby**: File pattern matching for source file discovery
- **zod**: Runtime schema validation for configuration

## Development Commands

```bash
# Install dependencies
pnpm install

# Build the package
pnpm build

# Watch mode for development
pnpm dev

# Lint and format code
pnpm lint
```

## Build System

- **TypeScript**: Modern ES2020 target with NodeNext module resolution
- **tsdown**: Fast TypeScript bundler for library builds, configured to output ESM format with minification
- **Biome**: Code formatting and linting with tab indentation and double quotes

## Configuration Schema

The package exports a `ConfigSchema` that defines:
- `source`: Array of glob patterns for source files
- `output`: Output path for generated CSS
- `buildOptions.reset`: Whether to include CSS reset (default: true)
- `buildOptions.minify`: Whether to minify output (default: false)

## Working with the Codebase

### Adding New Utility Support

To support new utility classes:
1. Utilities are defined in the `@yummacss/api` package
2. The generator automatically handles any utilities exported from `coreUtils()`
3. No changes needed in this package for new utility additions

### Modifying Extraction Logic

When updating class extraction patterns in `extractor.ts`:
- Test regex patterns against various file types (HTML, JSX, Vue, etc.)
- Ensure the filter logic correctly identifies valid Yumma CSS class names (lowercase start, contains hyphen)
- Consider performance impact on large codebases

### CSS Generation Updates

When modifying the generator:
- Maintain alphabetical sorting for consistent output
- Preserve CSS escaping for special characters (colons, slashes)
- Test with various class name patterns including variants and responsive prefixes

## Package Structure

```
src/
├── config/
│   └── schema.ts      # Configuration schema and types
├── reset/
│   └── base.ts        # CSS reset styles
├── extractor.ts       # Source file scanning and class extraction
├── generator.ts       # CSS rule generation from extracted classes
└── index.ts          # Public API exports
```

## Integration Notes

This package is designed to be used by CLI tools and build systems. It provides low-level functionality for:
- Scanning source files for Yumma CSS classes
- Generating corresponding CSS output
- Managing configuration and build options

The main CLI implementation is in the `yummacss` package, which depends on this engine.